﻿  base64.cpp
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\base64.cpp(60,65): warning C4267: '=': conversion from 'size_t' to 'unsigned char', possible loss of data
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\base64.cpp(71,64): warning C4267: '=': conversion from 'size_t' to 'unsigned char', possible loss of data
  Buff.cpp
  Chatbox.cpp
  Engine.cpp
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\VButtonFix.h(130,16): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Engine.cpp')
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xutility(4829,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'Engine.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xutility(4829,18):
      the template instantiation context (the oldest one first) is
          G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Engine.cpp(916,19):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Engine.cpp(916,19):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'GetWinBuild'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xstring(788,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned int,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned int
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xstring(944,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned int,
              _InIt=wchar_t *,
              _SizeTy=unsigned int
          ]
  
  Graphics.cpp
  Hooks.cpp
  Interface.cpp
  OnSend.cpp
  Packets.cpp
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Packets.cpp(496,38): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Packets.cpp(510,32): warning C4267: '=': conversion from 'size_t' to 'unsigned short', possible loss of data
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Packets.cpp(1338,44): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Packets.cpp(1362,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Packets.cpp(1673,35): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Packets.cpp(1684,37): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Packets.cpp(1689,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Packets.cpp(1713,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Packets.cpp(1745,35): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Packets.cpp(1777,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Packets.cpp(1783,42): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Packets.cpp(1828,40): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Packets.cpp(1851,42): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Packets.cpp(1856,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Packets.cpp(2118,45): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Packets.cpp(2170,46): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Packets.cpp(2211,44): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Packets.cpp(2216,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xutility(4829,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'Packets.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xutility(4829,18):
      the template instantiation context (the oldest one first) is
          G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Hardware.h(156,13):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Hardware.h(156,13):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'getHWID'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xstring(788,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned int,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned int
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xstring(944,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned int,
              _InIt=wchar_t *,
              _SizeTy=unsigned int
          ]
  
  Protect.cpp
  Tools.cpp
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Tools.cpp(414,15): warning C4302: 'type cast': truncation from 'void *' to 'BYTE'
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Engine\Tools.cpp(418,15): warning C4302: 'type cast': truncation from 'void *' to 'BYTE'
  unzip.cpp
  Variables.cpp
  zip.cpp
     Creating library G:\Kal\New Project\Cleaning Sources\Sources\Engine\Release\Engine.lib and object G:\Kal\New Project\Cleaning Sources\Sources\Engine\Release\Engine.exp
  Generating code
  Previous IPDB not found, fall back to full compilation.
  All 2636 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  Finished generating code
  Engine.vcxproj -> G:\Kal\New Project\Cleaning Sources\Sources\Engine\Release\Engine.dll
